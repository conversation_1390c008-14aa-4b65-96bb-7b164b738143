# frozen_string_literal: true

# Initialize consultation system notifications
Rails.application.config.after_initialize do
  # Create consultation notification types if they don't exist
  ConsultationNotificationService::NOTIFICATION_TYPES.each do |key, name|
    next if Notification.find_by(name: name)

    category = case key
               when :request_submitted, :request_approved, :request_declined, :consultation_completed
                 'Consultation'
               when :consultation_reminder
                 'Calendar'
               else
                 'Other'
               end

    Notification.create!(
      name: name,
      category: category,
      subject: name,
      main_link: '/consultations'
    )
  end

  # Schedule recurring consultation reminder job
  if Rails.env.production? || Rails.env.development?
    ConsultationReminderJob.schedule_recurring
  end
end
