# frozen_string_literal: true

class ConsultationsController < ApplicationController
  before_action :require_user

  # GET /consultations
  def index
    if current_user.is_student?
      redirect_to consultations_student_path
    elsif current_user.is_faculty?
      redirect_to consultations_faculty_path
    else
      flash[:error] = 'Access denied. You must be a student or faculty member to access consultations.'
      redirect_to root_path
    end
  end

  # GET /consultations/student
  def student
    unless current_user.is_student?
      flash[:error] = 'Access denied. Student access required.'
      redirect_to root_path
      return
    end

    @page_title = 'Student Consultations'
    @student_requests = current_user.student_consultation_requests.includes(:faculty, :faculty_time_slot)
                                   .order(created_at: :desc)
                                   .limit(10)

    js_env({
      STUDENT_CONSULTATIONS: {
        current_user_id: current_user.id,
        recent_requests: consultation_requests_json(@student_requests)
      }
    })

    js_bundle :student_consultations
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  # GET /consultations/faculty  
  def faculty
    unless current_user.is_faculty?
      flash[:error] = 'Access denied. Faculty access required.'
      redirect_to root_path
      return
    end

    @page_title = 'Faculty Consultations'
    @pending_requests = current_user.faculty_consultation_requests.pending.includes(:student, :faculty_time_slot)
    @upcoming_consultations = current_user.upcoming_consultations.limit(5)
    @statistics = current_user.consultation_statistics

    js_env({
      FACULTY_CONSULTATIONS: {
        current_user_id: current_user.id,
        pending_requests: consultation_requests_json(@pending_requests),
        upcoming_consultations: consultation_requests_json(@upcoming_consultations),
        statistics: @statistics
      }
    })

    js_bundle :faculty_consultations
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  # GET /consultations/navigation
  def navigation
    @page_title = 'Consultation System'
    
    @student_stats = if current_user.is_student?
      {
        total_requests: current_user.student_consultation_requests.count,
        pending_requests: current_user.student_consultation_requests.pending.count,
        upcoming_consultations: current_user.upcoming_consultations.count
      }
    else
      nil
    end

    @faculty_stats = if current_user.is_faculty?
      {
        pending_requests: current_user.faculty_consultation_requests.pending.count,
        upcoming_consultations: current_user.upcoming_consultations.count,
        total_time_slots: current_user.faculty_time_slots.count,
        available_time_slots: current_user.faculty_time_slots.available.count
      }
    else
      nil
    end

    js_env({
      CONSULTATION_NAVIGATION: {
        current_user_id: current_user.id,
        user_role: current_user.is_student? ? 'student' : (current_user.is_faculty? ? 'faculty' : 'other'),
        student_stats: @student_stats,
        faculty_stats: @faculty_stats
      }
    })

    js_bundle :consultation_navigation
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  private

  def consultation_requests_json(requests)
    requests.map do |request|
      {
        id: request.id,
        student_name: request.student_name,
        student_id: request.student_id,
        faculty_name: request.faculty.name,
        preferred_datetime: request.preferred_datetime.iso8601,
        formatted_datetime: request.formatted_preferred_datetime,
        description: request.description,
        nature_of_concern: request.nature_of_concern,
        concern_type_display: request.concern_type_display,
        status: request.status,
        status_display: request.status_display,
        faculty_comment: request.faculty_comment,
        created_at: request.created_at.iso8601,
        updated_at: request.updated_at.iso8601,
        can_be_approved: request.can_be_approved?,
        can_be_declined: request.can_be_declined?,
        can_be_completed: request.can_be_completed?
      }
    end
  end
end
