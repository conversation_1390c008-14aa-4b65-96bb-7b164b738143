# frozen_string_literal: true

class FacultyTimeSlot < ActiveRecord::Base
  belongs_to :user
  has_many :consultation_requests, dependent: :restrict_with_error

  DAYS_OF_WEEK = %w[Monday Tuesday Wednesday Thursday Friday Saturday Sunday].freeze

  validates :start_time, :end_time, :day_of_week, presence: true
  validates :day_of_week, inclusion: { in: DAYS_OF_WEEK }
  validates :is_recurring, :is_available, inclusion: { in: [true, false] }
  
  validate :end_time_after_start_time
  validate :specific_date_for_non_recurring
  validate :faculty_user_validation
  validate :no_overlapping_slots

  scope :available, -> { where(is_available: true) }
  scope :recurring, -> { where(is_recurring: true) }
  scope :specific_date, -> { where(is_recurring: false) }
  scope :for_day, ->(day) { where(day_of_week: day) }
  scope :for_date, ->(date) { where(specific_date: date) }
  scope :for_user, ->(user) { where(user: user) }
  scope :in_time_range, ->(start_time, end_time) { 
    where('start_time <= ? AND end_time >= ?', end_time, start_time) 
  }

  # Check if this time slot is available for booking at a specific datetime
  def available_at?(datetime)
    return false unless is_available?
    
    if is_recurring?
      # For recurring slots, check if the day matches
      datetime.strftime('%A') == day_of_week &&
        time_within_slot?(datetime)
    else
      # For specific date slots, check exact date and time
      specific_date == datetime.to_date &&
        time_within_slot?(datetime)
    end
  end

  # Check if there are any approved consultation requests for this slot at the given datetime
  def booked_at?(datetime)
    consultation_requests.where(
      status: ['approved', 'completed'],
      preferred_datetime: datetime.beginning_of_hour..datetime.end_of_hour
    ).exists?
  end

  # Get available time slots for a faculty member on a specific date
  def self.available_for_faculty_on_date(faculty_user, date)
    day_name = date.strftime('%A')
    
    # Get both recurring slots for the day and specific date slots
    recurring_slots = where(
      user: faculty_user,
      is_recurring: true,
      day_of_week: day_name,
      is_available: true
    )
    
    specific_slots = where(
      user: faculty_user,
      is_recurring: false,
      specific_date: date,
      is_available: true
    )
    
    (recurring_slots + specific_slots).uniq
  end

  # Generate available datetime options for a specific date
  def available_datetimes_for_date(date)
    return [] unless available_for_date?(date)
    
    datetimes = []
    current_time = Time.zone.parse("#{date} #{start_time.strftime('%H:%M')}")
    end_time_for_date = Time.zone.parse("#{date} #{end_time.strftime('%H:%M')}")
    
    # Generate 30-minute slots
    while current_time < end_time_for_date
      unless booked_at?(current_time)
        datetimes << current_time
      end
      current_time += 30.minutes
    end
    
    datetimes
  end

  private

  def end_time_after_start_time
    return unless start_time && end_time
    
    if end_time <= start_time
      errors.add(:end_time, 'must be after start time')
    end
  end

  def specific_date_for_non_recurring
    if !is_recurring? && specific_date.blank?
      errors.add(:specific_date, 'must be provided for non-recurring time slots')
    end
    
    if is_recurring? && specific_date.present?
      errors.add(:specific_date, 'should not be provided for recurring time slots')
    end
  end

  def faculty_user_validation
    return unless user
    
    # Check if user has faculty role (teacher enrollment)
    unless user.enrollments.where(type: 'TeacherEnrollment', workflow_state: 'active').exists?
      errors.add(:user, 'must be a faculty member')
    end
  end

  def no_overlapping_slots
    return unless user && start_time && end_time
    
    overlapping_query = self.class.where(user: user)
                                  .where.not(id: id) # Exclude current record for updates
                                  .where('start_time < ? AND end_time > ?', end_time, start_time)
    
    if is_recurring?
      overlapping_query = overlapping_query.where(
        is_recurring: true,
        day_of_week: day_of_week
      )
    else
      overlapping_query = overlapping_query.where(
        is_recurring: false,
        specific_date: specific_date
      )
    end
    
    if overlapping_query.exists?
      errors.add(:base, 'Time slot overlaps with existing slot')
    end
  end

  def time_within_slot?(datetime)
    slot_start = Time.zone.parse(start_time.strftime('%H:%M'))
    slot_end = Time.zone.parse(end_time.strftime('%H:%M'))
    check_time = Time.zone.parse(datetime.strftime('%H:%M'))
    
    check_time >= slot_start && check_time < slot_end
  end

  def available_for_date?(date)
    if is_recurring?
      date.strftime('%A') == day_of_week
    else
      specific_date == date
    end
  end
end
