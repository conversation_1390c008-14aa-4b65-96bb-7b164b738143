# frozen_string_literal: true

class ConsultationReminderJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info "Starting consultation reminder job"
    
    begin
      ConsultationNotificationService.schedule_consultation_reminders
      Rails.logger.info "Consultation reminder job completed successfully"
    rescue => e
      Rails.logger.error "Consultation reminder job failed: #{e.message}"
      raise e
    end
  end

  # Schedule this job to run every hour
  def self.schedule_recurring
    # Remove any existing scheduled jobs
    Delayed::Job.where(
      "handler LIKE '%ConsultationReminderJob%' AND run_at > ?"
    ).delete_all

    # Schedule the job to run every hour
    ConsultationReminderJob.set(wait: 1.hour).perform_later
  end
end
