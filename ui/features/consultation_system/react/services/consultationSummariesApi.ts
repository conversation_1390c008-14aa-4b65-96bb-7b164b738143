import { doFetch<PERSON><PERSON> } from '@canvas/do-fetch-api-effect'
import type { 
  ConsultationSummary,
  ConsultationSummaryResponse,
  ConsultationFilters
} from '../types'

const API_BASE = '/consultation_summaries'

export const fetchConsultationSummaries = async (filters?: ConsultationFilters): Promise<ConsultationSummaryResponse> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'GET',
      params: filters
    })
    return json as ConsultationSummaryResponse
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch consultation summaries')
  }
}

export const fetchConsultationSummary = async (id: string): Promise<ConsultationSummary> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'GET'
    })
    return json as ConsultationSummary
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch consultation summary')
  }
}

export const updateConsultationSummary = async (id: string, data: {
  outcome_summary?: string
  referral_made?: string
  follow_up_required?: string
}): Promise<ConsultationSummary> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'PATCH',
      body: {
        consultation_summary: data
      }
    })
    return json as ConsultationSummary
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to update consultation summary'
    throw new Error(errorMessage)
  }
}

export const addNotesToSummary = async (id: string, notes: string): Promise<ConsultationSummary> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}/add_notes`,
      method: 'POST',
      body: {
        notes: notes
      }
    })
    return json as ConsultationSummary
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to add notes'
    throw new Error(errorMessage)
  }
}

export const fetchSummariesDashboard = async (): Promise<{
  statistics: any
  concern_type_breakdown: Record<string, number>
  recent_summaries: ConsultationSummary[]
  follow_up_required: ConsultationSummary[]
}> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/dashboard`,
      method: 'GET'
    })
    return json
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch dashboard data')
  }
}

export const fetchSummariesReports = async (year?: number): Promise<{
  year: number
  monthly_data: Record<string, any>
  concern_type_stats: Record<string, number>
  total_consultations: number
}> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/reports`,
      method: 'GET',
      params: year ? { year: year.toString() } : {}
    })
    return json
  } catch (error: any) {
    throw new Error(error.message || 'Failed to fetch reports data')
  }
}
